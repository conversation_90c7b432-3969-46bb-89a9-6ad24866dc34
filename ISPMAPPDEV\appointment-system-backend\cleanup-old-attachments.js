// Script to clean up old Cloudinary attachments from database
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Attachment = require('./src/models/appointmentSchema/attachmentSchema');

async function cleanupOldAttachments() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to database');

    // Find all attachments with Cloudinary URLs
    const cloudinaryAttachments = await Attachment.find({
      'files.path': { $regex: 'cloudinary.com' }
    });

    console.log(`\n📊 Found ${cloudinaryAttachments.length} attachment records with Cloudinary URLs`);

    if (cloudinaryAttachments.length > 0) {
      console.log('\n📋 Cloudinary attachments found:');
      cloudinaryAttachments.forEach((attachment, index) => {
        console.log(`   ${index + 1}. ID: ${attachment._id}`);
        attachment.files.forEach((file, fileIndex) => {
          if (file.path.includes('cloudinary.com')) {
            console.log(`      File ${fileIndex + 1}: ${file.filename} -> ${file.path}`);
          }
        });
      });

      // Ask for confirmation (in a real scenario)
      console.log('\n⚠️  This script will DELETE all attachment records with Cloudinary URLs');
      console.log('   This is safe for testing but will remove old attachment data');
      console.log('   New uploads will use local storage and work properly');

      // Delete all Cloudinary attachments
      const deleteResult = await Attachment.deleteMany({
        'files.path': { $regex: 'cloudinary.com' }
      });

      console.log(`\n✅ Deleted ${deleteResult.deletedCount} attachment records with Cloudinary URLs`);
    } else {
      console.log('\n✅ No Cloudinary attachments found - database is clean!');
    }

    // Show remaining attachments (should be local storage ones)
    const remainingAttachments = await Attachment.find({});
    console.log(`\n📊 Remaining attachments: ${remainingAttachments.length}`);

    if (remainingAttachments.length > 0) {
      console.log('\n📋 Remaining attachments (should be local storage):');
      remainingAttachments.forEach((attachment, index) => {
        console.log(`   ${index + 1}. ID: ${attachment._id}`);
        attachment.files.forEach((file, fileIndex) => {
          const storageType = file.path.includes('/uploads/') ? 'LOCAL STORAGE' : 'UNKNOWN';
          console.log(`      File ${fileIndex + 1}: ${file.filename} -> ${storageType}`);
          console.log(`         URL: ${file.path}`);
        });
      });
    }

    console.log('\n🎯 Cleanup complete!');
    console.log('   - Old Cloudinary attachments removed');
    console.log('   - New uploads will use local storage');
    console.log('   - Admin panel should now show working attachment links');

  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n📤 Disconnected from database');
  }
}

// Run the cleanup
cleanupOldAttachments();
