import PropTypes from "prop-types";

const AttachmentDisplay = ({ attachmentUrls, maxWidth = "120px" }) => {
  if (!attachmentUrls || attachmentUrls === "No attachments" || attachmentUrls.trim() === "") {
    return (
      <span className="text-gray-500 text-sm">
        No attachments
      </span>
    );
  }

  // Handle potential errors in URL parsing
  try {
    console.log("📎 AttachmentDisplay received URLs:", attachmentUrls);
    
    return (
      <div className="flex flex-col gap-2">
        {attachmentUrls.split(", ").map((url, index) => {
          console.log(`📎 Processing URL ${index + 1}:`, url);
          
          // Handle both local file URLs and any remaining Cloudinary URLs
          let viewableUrl = url;
          let thumbnailUrl = url;
          let filename = `Attachment ${index + 1}`;

          // Check if it's a local file URL (our new implementation)
          if (url.includes("/uploads/attachments/")) {
            console.log(`📁 Local file detected: ${url}`);
            
            // Extract filename from the URL path
            const urlParts = url.split("/");
            const filenamePart = urlParts[urlParts.length - 1];
            
            // Parse the filename format: originalname-studentId-timestamp.ext
            const filenameMatch = filenamePart.match(/^(.+?)-[^-]+-\d+(\..+)$/);
            if (filenameMatch) {
              const originalName = filenameMatch[1].replace(/_/g, " ");
              const extension = filenameMatch[2];
              filename = originalName + extension;
            } else {
              // Fallback to the full filename
              filename = filenamePart;
            }
            
            // For local files, use the URL directly (no transformations needed)
            viewableUrl = url;
            thumbnailUrl = url; // We'll handle thumbnails with CSS
            
            console.log(`📁 Local file processed:`, { filename, viewableUrl });
            
          } else if (url.startsWith("https://res.cloudinary.com")) {
            console.log(`☁️ Cloudinary URL detected: ${url}`);
            
            // Handle legacy Cloudinary URLs (if any remain)
            const urlParts = url.split("/");
            const lastPart = urlParts[urlParts.length - 1];
            
            // Try to extract original filename from the public_id
            const publicIdMatch = lastPart.match(/^(.+?)-[^-]+-\d+$/);
            if (publicIdMatch) {
              let originalName = publicIdMatch[1].replace(/_/g, " ");
              
              // Try to determine file extension from the URL
              if (url.includes(".jpg") || url.includes(".jpeg")) {
                filename = originalName + ".jpg";
              } else if (url.includes(".png")) {
                filename = originalName + ".png";
              } else if (url.includes(".pdf")) {
                filename = originalName + ".pdf";
              } else {
                filename = originalName + ".jpg";
              }
            } else {
              filename = lastPart || `Attachment ${index + 1}`;
            }

            // Create Cloudinary transformation URLs
            thumbnailUrl = url.replace(
              "/upload/",
              "/upload/c_thumb,w_50,h_50,g_face/"
            );
            
            viewableUrl = url.replace(
              "/upload/",
              "/upload/c_scale,w_800,q_auto/"
            );
            
          } else {
            // Fallback for other URL types
            console.warn("⚠️ Unknown URL format:", url);
            filename = `Attachment ${index + 1}`;
          }

          return (
            <div
              key={index}
              className="flex items-center space-x-2 group"
            >
              {/* Thumbnail preview */}
              <img
                src={thumbnailUrl}
                alt="Attachment thumbnail"
                className="w-8 h-8 object-cover rounded border cursor-pointer hover:scale-110 transition-transform"
                onClick={() => window.open(viewableUrl, "_blank")}
                onError={(e) => {
                  console.error("❌ Failed to load thumbnail:", thumbnailUrl);
                  e.target.style.display = "none";
                }}
              />

              {/* Filename with truncation */}
              <div className="flex-1 min-w-0">
                <a
                  href={viewableUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline text-sm block"
                  title={filename}
                >
                  <span
                    data-tooltip-id="attachment-tooltip"
                    data-tooltip-content={filename}
                    className="cursor-help block truncate"
                    style={{ maxWidth }}
                  >
                    {filename}
                  </span>
                </a>
              </div>
            </div>
          );
        })}
      </div>
    );
  } catch (error) {
    console.error("❌ Error displaying attachments:", error);
    return (
      <span className="text-red-500 text-sm">
        Error loading attachments
      </span>
    );
  }
};

AttachmentDisplay.propTypes = {
  attachmentUrls: PropTypes.string,
  maxWidth: PropTypes.string,
};

export default AttachmentDisplay;
