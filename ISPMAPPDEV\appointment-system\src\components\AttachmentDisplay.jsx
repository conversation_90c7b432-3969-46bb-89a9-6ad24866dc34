import PropTypes from "prop-types";

const AttachmentDisplay = ({ attachmentUrls, maxWidth = "120px" }) => {
  if (
    !attachmentUrls ||
    attachmentUrls === "No attachments" ||
    attachmentUrls.trim() === ""
  ) {
    return <span className="text-gray-500 text-sm">No attachments</span>;
  }

  // Handle potential errors in URL parsing
  try {
    return (
      <div className="flex flex-col gap-2">
        {attachmentUrls.split(", ").map((url, index) => {
          // Since we now get full Cloudinary URLs from the backend,
          // we can use them directly
          let viewableUrl = url;
          let thumbnailUrl = url;
          let filename = `Attachment ${index + 1}`;

          // Check if it's a valid Cloudinary URL
          if (url.startsWith("https://res.cloudinary.com")) {
            // Extract filename from URL for display
            const urlParts = url.split("/");
            const lastPart = urlParts[urlParts.length - 1];

            // Try to extract original filename from the public_id
            const publicIdMatch = lastPart.match(/^(.+?)-\d+-\d+$/);
            if (publicIdMatch) {
              filename = publicIdMatch[1].replace(/_/g, " ") + ".jpg";
            } else {
              filename = lastPart || `Attachment ${index + 1}`;
            }

            // Create thumbnail URL with Cloudinary transformations
            thumbnailUrl = url.replace(
              "/upload/",
              "/upload/c_thumb,w_50,h_50,g_face/"
            );

            // Create viewable URL with better quality
            viewableUrl = url.replace(
              "/upload/",
              "/upload/c_scale,w_800,q_auto/"
            );
          } else {
            // Fallback for non-Cloudinary URLs (shouldn't happen now)
            console.warn("Non-Cloudinary URL detected:", url);
          }

          return (
            <div key={index} className="flex items-center space-x-2 group">
              {/* Thumbnail preview */}
              <img
                src={thumbnailUrl}
                alt="Attachment thumbnail"
                className="w-8 h-8 object-cover rounded border cursor-pointer hover:scale-110 transition-transform"
                onClick={() => window.open(viewableUrl, "_blank")}
                onError={(e) => {
                  e.target.style.display = "none";
                }}
              />

              {/* Filename with truncation */}
              <div className="flex-1 min-w-0">
                <a
                  href={viewableUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline text-sm block"
                  title={filename}
                >
                  <span
                    data-tooltip-id="attachment-tooltip"
                    data-tooltip-content={filename}
                    className="cursor-help block truncate"
                    style={{ maxWidth }}
                  >
                    {filename}
                  </span>
                </a>
              </div>
            </div>
          );
        })}
      </div>
    );
  } catch (error) {
    console.error("Error displaying attachments:", error);
    return (
      <span className="text-red-500 text-sm">Error loading attachments</span>
    );
  }
};

AttachmentDisplay.propTypes = {
  attachmentUrls: PropTypes.string,
  maxWidth: PropTypes.string,
};

export default AttachmentDisplay;
