# Protected Routes Implementation

This document explains the protected route functionality implemented for the admin side of the appointment system.

## Overview

The protected route system prevents unauthorized access to admin pages by:
1. Checking if the user is authenticated (has valid token and user data)
2. Validating the token with the server
3. Redirecting to the sign-in page if authentication fails
4. Preserving the originally requested URL for redirect after successful login

## Components

### 1. ProtectedRoute Component (`/src/components/ProtectedRoute.jsx`)

A wrapper component that protects admin routes by:
- Checking user authentication state from UserContext
- Validating JWT token with the server
- Showing a loading spinner during validation
- Redirecting to `/signin` if authentication fails
- Rendering the protected component if authentication succeeds

**Usage:**
```jsx
<Route 
  path="/registrarHome" 
  element={
    <ProtectedRoute>
      <RegistrarHome />
    </ProtectedRoute>
  } 
/>
```

### 2. useAuth Hook (`/src/hooks/useAuth.js`)

A custom hook that provides authentication utilities:
- `isAuthenticated()` - Checks if user is authenticated
- `hasToken()` - Checks if token exists in localStorage
- `clearAuth()` - Clears all authentication data
- `validateToken()` - Validates token with server
- Initializes axios headers on app startup

### 3. AuthProvider Component (`/src/components/AuthProvider.jsx`)

A wrapper component that ensures authentication initialization at the app level.

### 4. Enhanced UserContext (`/src/context/UserContext.jsx`)

Updated to listen for authentication expiration events and automatically log out users when tokens expire.

### 5. Enhanced SignIn Hook (`/src/features/admin/Login/hooks/useSignIn.js`)

Updated to redirect users to their originally requested page after successful login.

## Protected Routes

The following admin routes are now protected:
- `/registrarHome` - Main admin dashboard
- `/events` - Events management
- `/schedule` - Schedule management
- `/holidays` - Holiday management
- `/profile` - User profile
- `/appointments` - Appointments management
- `/archived` - Archived items
- `/feedback` - Feedback management
- `/announcements` - Announcements management

## How It Works

1. **Route Access**: When a user tries to access a protected route:
   - ProtectedRoute component checks authentication state
   - If not authenticated, redirects to `/signin` with the original URL stored in location state

2. **Authentication Validation**: 
   - Checks if user data exists in UserContext
   - Verifies JWT token exists in localStorage
   - Makes API call to validate token with server
   - Clears authentication data if token is invalid/expired

3. **Login Redirect**:
   - After successful login, user is redirected to originally requested page
   - Falls back to `/registrarHome` if no original URL was stored

4. **Token Expiration**:
   - When token validation fails, authentication data is cleared
   - Custom event is dispatched to notify UserContext
   - User is automatically logged out and redirected to sign-in

## Security Features

- **Server-side token validation**: Tokens are validated with the backend API
- **Automatic cleanup**: Invalid tokens and user data are automatically cleared
- **Axios header management**: Authorization headers are set/cleared automatically
- **Event-driven logout**: Token expiration triggers automatic logout
- **URL preservation**: Original requested URLs are preserved for post-login redirect

## Testing

To test the protected routes:

1. **Direct URL access**: Try accessing `/registrarHome` without being logged in
2. **Token expiration**: Manually clear the token from localStorage and try accessing protected routes
3. **Invalid token**: Modify the token in localStorage and try accessing protected routes
4. **Login redirect**: Access a protected route, get redirected to login, then verify redirect after successful login

## Troubleshooting

If protected routes aren't working:

1. Check browser console for authentication errors
2. Verify `VITE_API_URL` environment variable is set correctly
3. Ensure backend API endpoints are accessible
4. Check that JWT tokens are being stored in localStorage
5. Verify UserContext is properly wrapping the application
