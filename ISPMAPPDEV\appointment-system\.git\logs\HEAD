0000000000000000000000000000000000000000 99aebbee7a36bf2f12b66328a9f17552779e86bb <PERSON> <<EMAIL>> 1748652975 +0800	clone: from https://github.com/Grraffic/appointment-system.git
99aebbee7a36bf2f12b66328a9f17552779e86bb 9b913b061fe59ff6f813c3d45bdf0861a91a280f <PERSON> <<EMAIL>> 1748658037 +0800	pull origin main: Fast-forward
9b913b061fe59ff6f813c3d45bdf0861a91a280f 9b913b061fe59ff6f813c3d45bdf0861a91a280f <PERSON> <<EMAIL>> 1748663172 +0800	checkout: moving from main to render
9b913b061fe59ff6f813c3d45bdf0861a91a280f 9b913b061fe59ff6f813c3d45bdf0861a91a280f <PERSON> <<PERSON><EMAIL>> 1748797381 +0800	checkout: moving from render to main
9b913b061fe59ff6f813c3d45bdf0861a91a280f cfe071d3ae90d79dd1f0b1365847c9c318261f8c Rafael Ramos <<EMAIL>> 1748797391 +0800	pull origin main: Fast-forward
cfe071d3ae90d79dd1f0b1365847c9c318261f8c 9b913b061fe59ff6f813c3d45bdf0861a91a280f Rafael Ramos <<EMAIL>> 1748798507 +0800	checkout: moving from main to render
9b913b061fe59ff6f813c3d45bdf0861a91a280f cfe071d3ae90d79dd1f0b1365847c9c318261f8c Rafael Ramos <<EMAIL>> 1748800776 +0800	checkout: moving from render to main
cfe071d3ae90d79dd1f0b1365847c9c318261f8c b6f5782a4e60604beffca2fad07255d50e25bf70 Rafael Ramos <<EMAIL>> 1748802699 +0800	commit: Adding showing password in signup
b6f5782a4e60604beffca2fad07255d50e25bf70 b6f5782a4e60604beffca2fad07255d50e25bf70 Rafael Ramos <<EMAIL>> 1748807580 +0800	checkout: moving from main to bug
b6f5782a4e60604beffca2fad07255d50e25bf70 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1748813882 +0800	commit: Navigation
0a929398ff7edc56b9a53029a8596eabc6899f3e b6f5782a4e60604beffca2fad07255d50e25bf70 Rafael Ramos <<EMAIL>> 1748813885 +0800	checkout: moving from bug to main
b6f5782a4e60604beffca2fad07255d50e25bf70 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1748813892 +0800	merge bug: Fast-forward
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1748841384 +0800	checkout: moving from main to bugs
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1748864903 +0800	checkout: moving from bugs to main
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1748961989 +0800	checkout: moving from main to error
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749001181 +0800	checkout: moving from error to main
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749001358 +0800	checkout: moving from main to profile
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749002776 +0800	checkout: moving from profile to main
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749002927 +0800	checkout: moving from main to bug
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749003028 +0800	checkout: moving from bug to main
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749223861 +0800	checkout: moving from main to errors
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749229441 +0800	checkout: moving from errors to main
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749229594 +0800	checkout: moving from main to bugs
0a929398ff7edc56b9a53029a8596eabc6899f3e 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749254838 +0800	checkout: moving from bugs to main
0a929398ff7edc56b9a53029a8596eabc6899f3e e85fff4f182868fafc632261ee06a612098b2b33 Rafael Ramos <<EMAIL>> 1749354626 +0800	pull origin main: Fast-forward
e85fff4f182868fafc632261ee06a612098b2b33 e85fff4f182868fafc632261ee06a612098b2b33 Rafael Ramos <<EMAIL>> 1749378740 +0800	checkout: moving from main to errors
e85fff4f182868fafc632261ee06a612098b2b33 2e7a0aae1a5a1b00c2fb390f6e68781c849aab1d Rafael Ramos <<EMAIL>> 1749386067 +0800	commit: ??
2e7a0aae1a5a1b00c2fb390f6e68781c849aab1d e85fff4f182868fafc632261ee06a612098b2b33 Rafael Ramos <<EMAIL>> 1749386136 +0800	checkout: moving from errors to main
e85fff4f182868fafc632261ee06a612098b2b33 e85fff4f182868fafc632261ee06a612098b2b33 Rafael Ramos <<EMAIL>> 1749560792 +0800	reset: moving to HEAD
e85fff4f182868fafc632261ee06a612098b2b33 4b279c97d1b88d3fddf12c9c940ee3f0cd9e6537 Rafael Ramos <<EMAIL>> 1749560803 +0800	pull origin main: Fast-forward
4b279c97d1b88d3fddf12c9c940ee3f0cd9e6537 4b279c97d1b88d3fddf12c9c940ee3f0cd9e6537 Rafael Ramos <<EMAIL>> 1749561030 +0800	checkout: moving from main to debugging
4b279c97d1b88d3fddf12c9c940ee3f0cd9e6537 c1541cdb1dd905d1b729668b3ad266c6ca00a80f Rafael Ramos <<EMAIL>> 1749563140 +0800	commit: cloudinary
c1541cdb1dd905d1b729668b3ad266c6ca00a80f 4b279c97d1b88d3fddf12c9c940ee3f0cd9e6537 Rafael Ramos <<EMAIL>> 1749563183 +0800	checkout: moving from debugging to main
4b279c97d1b88d3fddf12c9c940ee3f0cd9e6537 c1541cdb1dd905d1b729668b3ad266c6ca00a80f Rafael Ramos <<EMAIL>> 1749563188 +0800	merge debugging: Fast-forward
c1541cdb1dd905d1b729668b3ad266c6ca00a80f c1541cdb1dd905d1b729668b3ad266c6ca00a80f Rafael Ramos <<EMAIL>> 1749564997 +0800	checkout: moving from main to buug
c1541cdb1dd905d1b729668b3ad266c6ca00a80f 80270c4734cf3b2c952a2d67664bdd368e61c818 Rafael Ramos <<EMAIL>> 1749567646 +0800	commit: remember me
80270c4734cf3b2c952a2d67664bdd368e61c818 c1541cdb1dd905d1b729668b3ad266c6ca00a80f Rafael Ramos <<EMAIL>> 1749567654 +0800	checkout: moving from buug to main
c1541cdb1dd905d1b729668b3ad266c6ca00a80f 80270c4734cf3b2c952a2d67664bdd368e61c818 Rafael Ramos <<EMAIL>> 1749567681 +0800	merge buug: Fast-forward
80270c4734cf3b2c952a2d67664bdd368e61c818 8c83fb6231a28a9b388a88f4130f67247bf3aaa5 Rafael Ramos <<EMAIL>> 1749568128 +0800	commit: deleting the unnecessary code
8c83fb6231a28a9b388a88f4130f67247bf3aaa5 8c83fb6231a28a9b388a88f4130f67247bf3aaa5 Rafael Ramos <<EMAIL>> 1749568309 +0800	checkout: moving from main to dashboard
8c83fb6231a28a9b388a88f4130f67247bf3aaa5 ce77eee1b67ce8c81ec0f7c038239cfa7a819b08 Rafael Ramos <<EMAIL>> 1749570305 +0800	commit: overview of the month
ce77eee1b67ce8c81ec0f7c038239cfa7a819b08 8c83fb6231a28a9b388a88f4130f67247bf3aaa5 Rafael Ramos <<EMAIL>> 1749570353 +0800	checkout: moving from dashboard to main
8c83fb6231a28a9b388a88f4130f67247bf3aaa5 ce77eee1b67ce8c81ec0f7c038239cfa7a819b08 Rafael Ramos <<EMAIL>> 1749570365 +0800	merge dashboard: Fast-forward
ce77eee1b67ce8c81ec0f7c038239cfa7a819b08 ce77eee1b67ce8c81ec0f7c038239cfa7a819b08 Rafael Ramos <<EMAIL>> 1749573211 +0800	checkout: moving from main to dashboard
ce77eee1b67ce8c81ec0f7c038239cfa7a819b08 89e48b32ca15c0c584f778ed622561271aeb307d Rafael Ramos <<EMAIL>> 1749575234 +0800	commit: duplicate
89e48b32ca15c0c584f778ed622561271aeb307d 3f08d8ffbef896a831d3691794b8e9115aee84c9 Rafael Ramos <<EMAIL>> 1749576452 +0800	commit: dynamic dashboard
3f08d8ffbef896a831d3691794b8e9115aee84c9 ce77eee1b67ce8c81ec0f7c038239cfa7a819b08 Rafael Ramos <<EMAIL>> 1749576455 +0800	checkout: moving from dashboard to main
ce77eee1b67ce8c81ec0f7c038239cfa7a819b08 3f08d8ffbef896a831d3691794b8e9115aee84c9 Rafael Ramos <<EMAIL>> 1749576461 +0800	merge dashboard: Fast-forward
3f08d8ffbef896a831d3691794b8e9115aee84c9 3f08d8ffbef896a831d3691794b8e9115aee84c9 Rafael Ramos <<EMAIL>> 1749576703 +0800	checkout: moving from main to dashboard
3f08d8ffbef896a831d3691794b8e9115aee84c9 0c4ea90392cb676ec78f5e9b43287a24f84afa5f Rafael Ramos <<EMAIL>> 1749577183 +0800	commit: dynamic
0c4ea90392cb676ec78f5e9b43287a24f84afa5f 3f08d8ffbef896a831d3691794b8e9115aee84c9 Rafael Ramos <<EMAIL>> 1749577187 +0800	checkout: moving from dashboard to main
3f08d8ffbef896a831d3691794b8e9115aee84c9 0c4ea90392cb676ec78f5e9b43287a24f84afa5f Rafael Ramos <<EMAIL>> 1749577190 +0800	merge dashboard: Fast-forward
0c4ea90392cb676ec78f5e9b43287a24f84afa5f f7d2ef0709fdd0f5ac48392d109fc2357c9906d7 Rafael Ramos <<EMAIL>> 1749578414 +0800	commit: delete
f7d2ef0709fdd0f5ac48392d109fc2357c9906d7 1ccc0203144a9a0238fc444b8d5249c7ff1e9314 Rafael Ramos <<EMAIL>> 1749580130 +0800	commit: fixing error
1ccc0203144a9a0238fc444b8d5249c7ff1e9314 0c4ea90392cb676ec78f5e9b43287a24f84afa5f Rafael Ramos <<EMAIL>> 1749580224 +0800	checkout: moving from main to dashboard
0c4ea90392cb676ec78f5e9b43287a24f84afa5f 1ccc0203144a9a0238fc444b8d5249c7ff1e9314 Rafael Ramos <<EMAIL>> 1749580503 +0800	checkout: moving from dashboard to main
1ccc0203144a9a0238fc444b8d5249c7ff1e9314 0c4ea90392cb676ec78f5e9b43287a24f84afa5f Rafael Ramos <<EMAIL>> 1749648514 +0800	checkout: moving from main to dashboard
0c4ea90392cb676ec78f5e9b43287a24f84afa5f 1ccc0203144a9a0238fc444b8d5249c7ff1e9314 Rafael Ramos <<EMAIL>> 1749648518 +0800	merge main: Fast-forward
1ccc0203144a9a0238fc444b8d5249c7ff1e9314 817330c25fc819a954096b458342cabec9de3787 Rafael Ramos <<EMAIL>> 1749650307 +0800	commit: dynamic carousel
817330c25fc819a954096b458342cabec9de3787 1ccc0203144a9a0238fc444b8d5249c7ff1e9314 Rafael Ramos <<EMAIL>> 1749650320 +0800	checkout: moving from dashboard to main
1ccc0203144a9a0238fc444b8d5249c7ff1e9314 817330c25fc819a954096b458342cabec9de3787 Rafael Ramos <<EMAIL>> 1749650331 +0800	merge dashboard: Fast-forward
817330c25fc819a954096b458342cabec9de3787 817330c25fc819a954096b458342cabec9de3787 Rafael Ramos <<EMAIL>> 1749650404 +0800	checkout: moving from main to dashboard
817330c25fc819a954096b458342cabec9de3787 5f62d888341394264c09762acfcc4488aeaceda3 Rafael Ramos <<EMAIL>> 1749653028 +0800	commit: facebook page
5f62d888341394264c09762acfcc4488aeaceda3 817330c25fc819a954096b458342cabec9de3787 Rafael Ramos <<EMAIL>> 1749653056 +0800	checkout: moving from dashboard to main
817330c25fc819a954096b458342cabec9de3787 5f62d888341394264c09762acfcc4488aeaceda3 Rafael Ramos <<EMAIL>> 1749653063 +0800	merge dashboard: Fast-forward
5f62d888341394264c09762acfcc4488aeaceda3 1cdfdbfcf6abcba25188523ff32c6d1bea629900 Rafael Ramos <<EMAIL>> 1749689389 +0800	pull: Fast-forward
1cdfdbfcf6abcba25188523ff32c6d1bea629900 5f62d888341394264c09762acfcc4488aeaceda3 Rafael Ramos <<EMAIL>> 1749689574 +0800	checkout: moving from main to dashboard
5f62d888341394264c09762acfcc4488aeaceda3 1cdfdbfcf6abcba25188523ff32c6d1bea629900 Rafael Ramos <<EMAIL>> 1749689586 +0800	merge main: Fast-forward
1cdfdbfcf6abcba25188523ff32c6d1bea629900 d5610216d1165124de64093132e1489eed32e40d Rafael Ramos <<EMAIL>> 1749691454 +0800	commit: minor changes
d5610216d1165124de64093132e1489eed32e40d 1cdfdbfcf6abcba25188523ff32c6d1bea629900 Rafael Ramos <<EMAIL>> 1749693405 +0800	checkout: moving from dashboard to main
1cdfdbfcf6abcba25188523ff32c6d1bea629900 d5610216d1165124de64093132e1489eed32e40d Rafael Ramos <<EMAIL>> 1749693416 +0800	merge dashboard: Fast-forward
d5610216d1165124de64093132e1489eed32e40d d5610216d1165124de64093132e1489eed32e40d Rafael Ramos <<EMAIL>> 1749693456 +0800	checkout: moving from main to dashboard
d5610216d1165124de64093132e1489eed32e40d d5610216d1165124de64093132e1489eed32e40d Rafael Ramos <<EMAIL>> 1749695400 +0800	checkout: moving from dashboard to main
d5610216d1165124de64093132e1489eed32e40d d5610216d1165124de64093132e1489eed32e40d Rafael Ramos <<EMAIL>> 1749695405 +0800	reset: moving to HEAD
d5610216d1165124de64093132e1489eed32e40d c6d2fe7587f77e7f6542f316ac15239b466e97b0 Rafael Ramos <<EMAIL>> 1749731978 +0800	pull origin main: Fast-forward
c6d2fe7587f77e7f6542f316ac15239b466e97b0 d5610216d1165124de64093132e1489eed32e40d Rafael Ramos <<EMAIL>> 1749732008 +0800	checkout: moving from main to dashboard
d5610216d1165124de64093132e1489eed32e40d c6d2fe7587f77e7f6542f316ac15239b466e97b0 Rafael Ramos <<EMAIL>> 1749732023 +0800	merge main: Fast-forward
c6d2fe7587f77e7f6542f316ac15239b466e97b0 cc42f98b15b8102dc119b2b05166560bce6d3961 Rafael Ramos <<EMAIL>> 1749733341 +0800	commit: announcement page
cc42f98b15b8102dc119b2b05166560bce6d3961 c6d2fe7587f77e7f6542f316ac15239b466e97b0 Rafael Ramos <<EMAIL>> 1749733354 +0800	checkout: moving from dashboard to main
c6d2fe7587f77e7f6542f316ac15239b466e97b0 cc42f98b15b8102dc119b2b05166560bce6d3961 Rafael Ramos <<EMAIL>> 1749733360 +0800	merge dashboard: Fast-forward
cc42f98b15b8102dc119b2b05166560bce6d3961 cc42f98b15b8102dc119b2b05166560bce6d3961 Rafael Ramos <<EMAIL>> 1749733400 +0800	checkout: moving from main to dashboard
cc42f98b15b8102dc119b2b05166560bce6d3961 fd56646ec2297736d5e70955ab3c88de159294b0 Rafael Ramos <<EMAIL>> 1749734689 +0800	commit: not fix
fd56646ec2297736d5e70955ab3c88de159294b0 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749734697 +0800	checkout: moving from dashboard to bug
0a929398ff7edc56b9a53029a8596eabc6899f3e fd56646ec2297736d5e70955ab3c88de159294b0 Rafael Ramos <<EMAIL>> 1749734813 +0800	checkout: moving from bug to dashboard
fd56646ec2297736d5e70955ab3c88de159294b0 fd56646ec2297736d5e70955ab3c88de159294b0 Rafael Ramos <<EMAIL>> 1749735550 +0800	reset: moving to HEAD
fd56646ec2297736d5e70955ab3c88de159294b0 c029aac312b6e3efce3537a98111109135d415bd Rafael Ramos <<EMAIL>> 1749741785 +0800	commit: di ko alam
c029aac312b6e3efce3537a98111109135d415bd cc42f98b15b8102dc119b2b05166560bce6d3961 Rafael Ramos <<EMAIL>> 1749741808 +0800	checkout: moving from dashboard to main
cc42f98b15b8102dc119b2b05166560bce6d3961 cc42f98b15b8102dc119b2b05166560bce6d3961 Rafael Ramos <<EMAIL>> 1749743357 +0800	reset: moving to HEAD
cc42f98b15b8102dc119b2b05166560bce6d3961 c029aac312b6e3efce3537a98111109135d415bd Rafael Ramos <<EMAIL>> 1749743716 +0800	checkout: moving from main to dashboard
c029aac312b6e3efce3537a98111109135d415bd 1943c18c8a3d552f1b2d6da3fcb518190e07d4bc Rafael Ramos <<EMAIL>> 1749743771 +0800	commit: announcment
1943c18c8a3d552f1b2d6da3fcb518190e07d4bc 7cc0a837106ac8903f7ccdfad97b104fe59b365f Rafael Ramos <<EMAIL>> 1749743842 +0800	commit: minorchanges
7cc0a837106ac8903f7ccdfad97b104fe59b365f 7cc0a837106ac8903f7ccdfad97b104fe59b365f Rafael Ramos <<EMAIL>> 1749746835 +0800	reset: moving to HEAD
7cc0a837106ac8903f7ccdfad97b104fe59b365f cc42f98b15b8102dc119b2b05166560bce6d3961 Rafael Ramos <<EMAIL>> 1749746903 +0800	checkout: moving from dashboard to main
cc42f98b15b8102dc119b2b05166560bce6d3961 6f04ed26cf11cd4b5d8dcf2e41b0c162032116a6 Rafael Ramos <<EMAIL>> 1749771313 +0800	commit (merge): UPDATED
6f04ed26cf11cd4b5d8dcf2e41b0c162032116a6 b29634b0ff271f154fac520a525d53e3f2d2ae7b Rafael Ramos <<EMAIL>> 1749772381 +0800	commit: events
b29634b0ff271f154fac520a525d53e3f2d2ae7b 3378125ff243b05fcebea90a120f06104fd92000 Rafael Ramos <<EMAIL>> 1749772790 +0800	commit: holiday error
3378125ff243b05fcebea90a120f06104fd92000 3378125ff243b05fcebea90a120f06104fd92000 Rafael Ramos <<EMAIL>> 1749773995 +0800	checkout: moving from main to checking
3378125ff243b05fcebea90a120f06104fd92000 f47a94704a340b8ff61db344f74f319e1a6be31d Rafael Ramos <<EMAIL>> 1749775264 +0800	commit: minor
f47a94704a340b8ff61db344f74f319e1a6be31d 3378125ff243b05fcebea90a120f06104fd92000 Rafael Ramos <<EMAIL>> 1749775303 +0800	checkout: moving from checking to main
3378125ff243b05fcebea90a120f06104fd92000 6cfe837ae817832c52fdd9c02da712b24a71312b Rafael Ramos <<EMAIL>> 1749964163 +0800	commit: minor changes
6cfe837ae817832c52fdd9c02da712b24a71312b 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749964227 +0800	checkout: moving from main to bug
0a929398ff7edc56b9a53029a8596eabc6899f3e 6cfe837ae817832c52fdd9c02da712b24a71312b Rafael Ramos <<EMAIL>> 1749964287 +0800	checkout: moving from bug to main
6cfe837ae817832c52fdd9c02da712b24a71312b 6cfe837ae817832c52fdd9c02da712b24a71312b Rafael Ramos <<EMAIL>> 1749964315 +0800	checkout: moving from main to revert
6cfe837ae817832c52fdd9c02da712b24a71312b 6cfe837ae817832c52fdd9c02da712b24a71312b Rafael Ramos <<EMAIL>> 1749964485 +0800	checkout: moving from revert to main
6cfe837ae817832c52fdd9c02da712b24a71312b 2f97f4a3b68d21763f0200ce7bf0e62de8ce2295 Rafael Ramos <<EMAIL>> 1749964586 +0800	revert: Revert "facebook page"
2f97f4a3b68d21763f0200ce7bf0e62de8ce2295 2f97f4a3b68d21763f0200ce7bf0e62de8ce2295 Rafael Ramos <<EMAIL>> 1749965606 +0800	checkout: moving from main to new
2f97f4a3b68d21763f0200ce7bf0e62de8ce2295 2f97f4a3b68d21763f0200ce7bf0e62de8ce2295 Rafael Ramos <<EMAIL>> 1749968650 +0800	reset: moving to HEAD
2f97f4a3b68d21763f0200ce7bf0e62de8ce2295 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749968990 +0800	commit: dashboard
211f4798fbfd435904d8f7e2153b915f5cc69edb 2f97f4a3b68d21763f0200ce7bf0e62de8ce2295 Rafael Ramos <<EMAIL>> 1749969019 +0800	checkout: moving from new to main
2f97f4a3b68d21763f0200ce7bf0e62de8ce2295 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749969023 +0800	merge new: Fast-forward
211f4798fbfd435904d8f7e2153b915f5cc69edb 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749970227 +0800	checkout: moving from main to bug
0a929398ff7edc56b9a53029a8596eabc6899f3e 6cfe837ae817832c52fdd9c02da712b24a71312b Rafael Ramos <<EMAIL>> 1749970280 +0800	checkout: moving from bug to revert
6cfe837ae817832c52fdd9c02da712b24a71312b 53bc1863632a158b412351777921a1bb6ae62c95 Rafael Ramos <<EMAIL>> 1749970352 +0800	commit: dashboard
53bc1863632a158b412351777921a1bb6ae62c95 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749970365 +0800	checkout: moving from revert to bug
0a929398ff7edc56b9a53029a8596eabc6899f3e 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749970567 +0800	checkout: moving from bug to new
211f4798fbfd435904d8f7e2153b915f5cc69edb 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749970673 +0800	reset: moving to HEAD
211f4798fbfd435904d8f7e2153b915f5cc69edb 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749970684 +0800	checkout: moving from new to bug
0a929398ff7edc56b9a53029a8596eabc6899f3e 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749970761 +0800	checkout: moving from bug to new
211f4798fbfd435904d8f7e2153b915f5cc69edb fe20c09875ee8bb4551452fc77c4f9c7056462d6 Rafael Ramos <<EMAIL>> 1749970914 +0800	commit: updated
fe20c09875ee8bb4551452fc77c4f9c7056462d6 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749970931 +0800	checkout: moving from new to bug
0a929398ff7edc56b9a53029a8596eabc6899f3e fe20c09875ee8bb4551452fc77c4f9c7056462d6 Rafael Ramos <<EMAIL>> 1749970966 +0800	checkout: moving from bug to new
fe20c09875ee8bb4551452fc77c4f9c7056462d6 1fd32766d7d4e283bcbf1d49f241381be368b3f2 Rafael Ramos <<EMAIL>> 1749971229 +0800	commit: dynamic dashboard
1fd32766d7d4e283bcbf1d49f241381be368b3f2 5afd851387e8093e7bdeae25d6537655914556dd Rafael Ramos <<EMAIL>> 1749972549 +0800	commit: dynamic dashboard
5afd851387e8093e7bdeae25d6537655914556dd af7d793b2c39f6a40c74f9bc5430f686e16e34ab Rafael Ramos <<EMAIL>> 1749973137 +0800	commit: dashboard
af7d793b2c39f6a40c74f9bc5430f686e16e34ab 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749973170 +0800	checkout: moving from new to main
211f4798fbfd435904d8f7e2153b915f5cc69edb 7cc0a837106ac8903f7ccdfad97b104fe59b365f Rafael Ramos <<EMAIL>> 1749973205 +0800	checkout: moving from main to dashboard
7cc0a837106ac8903f7ccdfad97b104fe59b365f af7d793b2c39f6a40c74f9bc5430f686e16e34ab Rafael Ramos <<EMAIL>> 1749973323 +0800	checkout: moving from dashboard to new
af7d793b2c39f6a40c74f9bc5430f686e16e34ab a7c8faa838e01a8be80d589a130f81ed7f628af0 Rafael Ramos <<EMAIL>> 1749975312 +0800	commit: facebook page dynamic
a7c8faa838e01a8be80d589a130f81ed7f628af0 0a929398ff7edc56b9a53029a8596eabc6899f3e Rafael Ramos <<EMAIL>> 1749975338 +0800	checkout: moving from new to bug
0a929398ff7edc56b9a53029a8596eabc6899f3e a7c8faa838e01a8be80d589a130f81ed7f628af0 Rafael Ramos <<EMAIL>> 1749975500 +0800	checkout: moving from bug to new
a7c8faa838e01a8be80d589a130f81ed7f628af0 0d502a3ff62aa8d77f42ceb23fa994a8cf916d20 Rafael Ramos <<EMAIL>> 1749978458 +0800	commit: deleting the duplicate appointment
0d502a3ff62aa8d77f42ceb23fa994a8cf916d20 0d502a3ff62aa8d77f42ceb23fa994a8cf916d20 Rafael Ramos <<EMAIL>> 1749981781 +0800	reset: moving to HEAD
0d502a3ff62aa8d77f42ceb23fa994a8cf916d20 032150f659dcb1d8c3c1b9f05727dfb6491184a0 Rafael Ramos <<EMAIL>> 1749983890 +0800	commit: filter
032150f659dcb1d8c3c1b9f05727dfb6491184a0 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749984802 +0800	checkout: moving from new to main
211f4798fbfd435904d8f7e2153b915f5cc69edb 032150f659dcb1d8c3c1b9f05727dfb6491184a0 Rafael Ramos <<EMAIL>> 1749985108 +0800	checkout: moving from main to new
032150f659dcb1d8c3c1b9f05727dfb6491184a0 032150f659dcb1d8c3c1b9f05727dfb6491184a0 Rafael Ramos <<EMAIL>> 1749985118 +0800	checkout: moving from new to attachment
032150f659dcb1d8c3c1b9f05727dfb6491184a0 032150f659dcb1d8c3c1b9f05727dfb6491184a0 Rafael Ramos <<EMAIL>> 1749988169 +0800	checkout: moving from attachment to new
032150f659dcb1d8c3c1b9f05727dfb6491184a0 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749988334 +0800	checkout: moving from new to main
211f4798fbfd435904d8f7e2153b915f5cc69edb 211f4798fbfd435904d8f7e2153b915f5cc69edb Rafael Ramos <<EMAIL>> 1749988360 +0800	checkout: moving from main to main2
211f4798fbfd435904d8f7e2153b915f5cc69edb 032150f659dcb1d8c3c1b9f05727dfb6491184a0 Rafael Ramos <<EMAIL>> 1749988406 +0800	checkout: moving from main2 to new
032150f659dcb1d8c3c1b9f05727dfb6491184a0 032150f659dcb1d8c3c1b9f05727dfb6491184a0 Rafael Ramos <<EMAIL>> 1749988433 +0800	checkout: moving from new to new2
032150f659dcb1d8c3c1b9f05727dfb6491184a0 032150f659dcb1d8c3c1b9f05727dfb6491184a0 Rafael Ramos <<EMAIL>> 1749994129 +0800	reset: moving to HEAD
032150f659dcb1d8c3c1b9f05727dfb6491184a0 032150f659dcb1d8c3c1b9f05727dfb6491184a0 Rafael Ramos <<EMAIL>> 1749994143 +0800	reset: moving to HEAD
032150f659dcb1d8c3c1b9f05727dfb6491184a0 7cc0a837106ac8903f7ccdfad97b104fe59b365f Rafael Ramos <<EMAIL>> 1749997695 +0800	checkout: moving from new2 to dashboard
7cc0a837106ac8903f7ccdfad97b104fe59b365f d80d48c6e032da3d882814d2097177714480260f Rafael Ramos <<EMAIL>> 1749999356 +0800	commit: minor CHANGES
d80d48c6e032da3d882814d2097177714480260f 43840bb914d8726299bc20fec95d70e6f423ae27 Rafael Ramos <<EMAIL>> 1750000241 +0800	commit: dashboard
43840bb914d8726299bc20fec95d70e6f423ae27 43840bb914d8726299bc20fec95d70e6f423ae27 Rafael Ramos <<EMAIL>> 1750015862 +0800	reset: moving to HEAD
43840bb914d8726299bc20fec95d70e6f423ae27 d80d48c6e032da3d882814d2097177714480260f Rafael Ramos <<EMAIL>> 1750016541 +0800	reset: moving to d80d48c
d80d48c6e032da3d882814d2097177714480260f 26933ed4ef98b47224edd76a9df8b38d816507a5 Rafael Ramos <<EMAIL>> 1750017016 +0800	commit: dynamic dashboard june 16
26933ed4ef98b47224edd76a9df8b38d816507a5 26933ed4ef98b47224edd76a9df8b38d816507a5 Rafael Ramos <<EMAIL>> 1750018699 +0800	checkout: moving from dashboard to update
26933ed4ef98b47224edd76a9df8b38d816507a5 8f89e17f2059df9a097e2b66393d3b36746d3648 Rafael Ramos <<EMAIL>> 1750022432 +0800	commit: hover for purpose
8f89e17f2059df9a097e2b66393d3b36746d3648 c700b82a4f826b3191d5d9c97dc3f0809dd77ff0 Rafael Ramos <<EMAIL>> 1750023224 +0800	commit: profile bugs
c700b82a4f826b3191d5d9c97dc3f0809dd77ff0 26933ed4ef98b47224edd76a9df8b38d816507a5 Rafael Ramos <<EMAIL>> 1750027545 +0800	checkout: moving from update to dashboard
26933ed4ef98b47224edd76a9df8b38d816507a5 c700b82a4f826b3191d5d9c97dc3f0809dd77ff0 Rafael Ramos <<EMAIL>> 1750027550 +0800	merge update: Fast-forward
c700b82a4f826b3191d5d9c97dc3f0809dd77ff0 c700b82a4f826b3191d5d9c97dc3f0809dd77ff0 Rafael Ramos <<EMAIL>> 1750029282 +0800	checkout: moving from dashboard to update
c700b82a4f826b3191d5d9c97dc3f0809dd77ff0 cf4ee2c05936f90f9320757a925316577b130304 Rafael Ramos <<EMAIL>> 1750087254 +0800	commit: design minor change
