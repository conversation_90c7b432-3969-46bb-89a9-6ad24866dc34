const express = require("express");
const router = express.Router();
const multer = require("multer");
const { attachmentStorage } = require("../../config/cloudinary");

console.log(
  "📁 Attachment route loaded - using storage:",
  typeof attachmentStorage
);
const {
  uploadAttachments,
  getAllAttachments,
  getAttachmentById,
  deleteAttachmentById,
} = require("../../controllers/appointmentController/attachment.Controller");

const fileFilter = (req, file, cb) => {
  // Accept images and PDFs
  if (
    file.mimetype.startsWith("image/") ||
    file.mimetype === "application/pdf"
  ) {
    cb(null, true);
  } else {
    cb(new Error("Only images and PDF files are allowed!"), false);
  }
};

const upload = multer({
  storage: attachmentStorage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
}).array("files", 10);

// Error handling middleware
const uploadMiddleware = (req, res, next) => {
  upload(req, res, (err) => {
    if (err instanceof multer.MulterError) {
      // Multer error occurred
      return res.status(400).json({
        message: "File upload error",
        error: err.message,
      });
    } else if (err) {
      // Other error occurred
      return res.status(400).json({
        message: "Error uploading file",
        error: err.message,
      });
    }
    // Everything went fine
    next();
  });
};

// Routes
router.post("/upload", uploadMiddleware, uploadAttachments);
router.get("/", getAllAttachments);
router.get("/:id", getAttachmentById);
router.delete("/:id", deleteAttachmentById);

// Route to serve individual files with proper headers
router.get("/file/:filename", (req, res) => {
  try {
    const path = require("path");
    const fs = require("fs");

    const filename = req.params.filename;
    const filePath = path.join(
      __dirname,
      "../../../uploads/attachments",
      filename
    );

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ message: "File not found" });
    }

    // Get file stats
    const stat = fs.statSync(filePath);

    // Set appropriate headers
    res.setHeader("Content-Length", stat.size);
    res.setHeader("Content-Type", "application/octet-stream");
    res.setHeader("Content-Disposition", `inline; filename="${filename}"`);

    // Stream the file
    const readStream = fs.createReadStream(filePath);
    readStream.pipe(res);
  } catch (error) {
    console.error("Error serving file:", error);
    res.status(500).json({ message: "Error serving file" });
  }
});

module.exports = router;
