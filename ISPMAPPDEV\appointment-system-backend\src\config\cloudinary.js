const cloudinary = require("cloudinary").v2;
const { CloudinaryStorage } = require("multer-storage-cloudinary");

if (
  !process.env.CLOUDINARY_CLOUD_NAME ||
  !process.env.CLOUDINARY_API_KEY ||
  !process.env.CLOUDINARY_API_SECRET
) {
  console.error(
    "Missing required environment variables. Using fallback configuration."
  );
}

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Configure Cloudinary storage for profile pictures
let profilePictureStorage;

try {
  if (
    process.env.CLOUDINARY_CLOUD_NAME &&
    process.env.CLOUDINARY_API_KEY &&
    process.env.CLOUDINARY_API_SECRET
  ) {
    profilePictureStorage = new CloudinaryStorage({
      cloudinary: cloudinary,
      params: {
        folder: "appointment-system/profile-pictures",
        allowed_formats: ["jpg", "jpeg", "png", "gif", "webp"],
        transformation: [
          { width: 400, height: 400, crop: "fill", quality: "auto" },
        ],
        public_id: (req, file) => {
          const userId = req.params.userId;
          return `profile-${userId}-${Date.now()}`;
        },
      },
    });
  } else {
    // Fallback to memory storage
    const multer = require("multer");
    profilePictureStorage = multer.memoryStorage();
  }
} catch (error) {
  console.error("❌ Error configuring Cloudinary storage:", error);
  // Fallback to memory storage
  const multer = require("multer");
  profilePictureStorage = multer.memoryStorage();
}

// Configure local storage for attachments (replacing Cloudinary)
const multer = require("multer");
const path = require("path");
const fs = require("fs");

// Create uploads directory if it doesn't exist
const uploadsDir = path.join(__dirname, "../../uploads/attachments");
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log("✅ Created attachments upload directory:", uploadsDir);
}

const attachmentStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, uploadsDir);
  },
  filename: function (req, file, cb) {
    const studentId = req.body.studentId;
    // Get file extension
    const ext = path.extname(file.originalname);
    // Remove file extension and special characters from original filename
    const originalName = file.originalname
      .replace(/\.[^/.]+$/, "")
      .replace(/[^a-zA-Z0-9]/g, "_");
    // Create unique filename: originalname-studentId-timestamp.ext
    const filename = `${originalName}-${studentId}-${Date.now()}${ext}`;
    cb(null, filename);
  },
});

module.exports = {
  cloudinary,
  profilePictureStorage,
  attachmentStorage,
};
