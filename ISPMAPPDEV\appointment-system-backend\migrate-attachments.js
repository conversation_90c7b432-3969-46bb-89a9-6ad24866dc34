// Script to migrate old Cloudinary attachments to show as "No attachments"
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Attachment = require('./src/models/appointmentSchema/attachmentSchema');

async function migrateAttachments() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to database');

    // Find all attachments with Cloudinary URLs
    const cloudinaryAttachments = await Attachment.find({
      'files.path': { $regex: 'cloudinary.com' }
    });

    console.log(`\n📊 Found ${cloudinaryAttachments.length} attachment records with Cloudinary URLs`);

    if (cloudinaryAttachments.length > 0) {
      console.log('\n📋 Migrating Cloudinary attachments:');
      
      for (const attachment of cloudinaryAttachments) {
        console.log(`   Processing attachment ID: ${attachment._id}`);
        
        // Update each file in the attachment
        attachment.files.forEach((file, index) => {
          if (file.path.includes('cloudinary.com')) {
            console.log(`      File ${index + 1}: ${file.filename} (Cloudinary) -> Marking as unavailable`);
            // Mark as unavailable but keep the record
            file.path = 'UNAVAILABLE_CLOUDINARY_FILE';
            file.filename = `${file.filename} (File no longer available)`;
          }
        });

        // Save the updated attachment
        await attachment.save();
        console.log(`   ✅ Updated attachment ${attachment._id}`);
      }

      console.log(`\n✅ Migrated ${cloudinaryAttachments.length} attachment records`);
    } else {
      console.log('\n✅ No Cloudinary attachments found - database is clean!');
    }

    // Show all attachments after migration
    const allAttachments = await Attachment.find({});
    console.log(`\n📊 Total attachments after migration: ${allAttachments.length}`);

    console.log('\n🎯 Migration complete!');
    console.log('   - Old Cloudinary attachments marked as unavailable');
    console.log('   - New uploads will use local storage');
    console.log('   - Admin panel will show "File no longer available" for old attachments');

  } catch (error) {
    console.error('❌ Error during migration:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n📤 Disconnected from database');
  }
}

// Run the migration
migrateAttachments();
