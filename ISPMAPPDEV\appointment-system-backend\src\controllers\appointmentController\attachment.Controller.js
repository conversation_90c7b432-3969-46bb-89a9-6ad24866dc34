const Student = require("../../models/appointmentSchema/studentSchema");
const Attachment = require("../../models/appointmentSchema/attachmentSchema");

// Upload new attachments
exports.uploadAttachments = async (req, res) => {
  try {
    console.log("Processing attachment upload...");

    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ message: "No files uploaded" });
    }

    const studentId = req.body.studentId;
    if (!studentId) {
      return res.status(400).json({ message: "Student ID is required" });
    }

    console.log(`Processing ${req.files.length} files for student:`, studentId);

    const files = req.files.map((file) => {
      console.log(`File processed:`, {
        originalName: file.originalname,
        cloudinaryPath: file.path,
        cloudinaryUrl: file.path,
        size: file.size,
      });

      return {
        student: studentId,
        filename: file.originalname, // Keep original filename for display
        path: file.path, // This should be the Cloudinary URL
        mimetype: file.mimetype,
        size: file.size,
      };
    });

    const newAttachment = new Attachment({ files });
    await newAttachment.save();

    console.log("✅ Attachment saved successfully with Cloudinary URLs:");
    files.forEach((f, index) => {
      console.log(`   File ${index + 1}:`);
      console.log(`     Original: ${f.filename}`);
      console.log(`     Cloudinary URL: ${f.path}`);
      console.log(
        `     Valid Cloudinary: ${f.path.includes("cloudinary.com")}`
      );
    });

    res.status(201).json({
      message: "Files uploaded successfully",
      data: newAttachment,
      files: files.map((f) => ({
        originalName: f.filename,
        url: f.path,
        size: f.size,
        type: f.mimetype,
      })),
    });
  } catch (error) {
    console.error("❌ Upload Error:", error);
    res
      .status(500)
      .json({ message: "Error uploading attachments", error: error.message });
  }
};

// Get all attachments
exports.getAllAttachments = async (req, res) => {
  try {
    const attachments = await Attachment.find().populate(
      "files.student",
      "name email"
    );
    res.status(200).json({ data: attachments });
  } catch (error) {
    res.status(500).json({ message: "Error fetching attachments", error });
  }
};

// Get attachment by ID
exports.getAttachmentById = async (req, res) => {
  try {
    const attachment = await Attachment.findById(req.params.id).populate(
      "files.student"
    );
    if (!attachment) {
      return res.status(404).json({ message: "Attachment not found" });
    }
    res.status(200).json({ data: attachment });
  } catch (error) {
    res.status(500).json({ message: "Error retrieving attachment", error });
  }
};

// Delete attachment by ID
exports.deleteAttachmentById = async (req, res) => {
  try {
    const attachment = await Attachment.findByIdAndDelete(req.params.id);
    if (!attachment) {
      return res.status(404).json({ message: "Attachment not found" });
    }
    res.status(200).json({ message: "Attachment deleted successfully" });
  } catch (error) {
    res.status(500).json({ message: "Error deleting attachment", error });
  }
};
