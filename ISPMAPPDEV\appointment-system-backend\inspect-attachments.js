// Script to inspect current attachments in database
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Attachment = require('./src/models/appointmentSchema/attachmentSchema');

async function inspectAttachments() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to database');

    // Get all attachments
    const allAttachments = await Attachment.find({});
    console.log(`\n📊 Total attachment records: ${allAttachments.length}`);

    if (allAttachments.length === 0) {
      console.log('📂 No attachments found in database');
      return;
    }

    let cloudinaryCount = 0;
    let localCount = 0;
    let otherCount = 0;

    console.log('\n📋 Attachment details:');
    allAttachments.forEach((attachment, index) => {
      console.log(`\n   ${index + 1}. Attachment ID: ${attachment._id}`);
      console.log(`      Created: ${attachment.files[0]?.uploadedAt || 'Unknown'}`);
      
      attachment.files.forEach((file, fileIndex) => {
        let storageType = 'UNKNOWN';
        if (file.path.includes('cloudinary.com')) {
          storageType = 'CLOUDINARY';
          cloudinaryCount++;
        } else if (file.path.includes('/uploads/')) {
          storageType = 'LOCAL STORAGE';
          localCount++;
        } else {
          otherCount++;
        }

        console.log(`      File ${fileIndex + 1}:`);
        console.log(`         Name: ${file.filename}`);
        console.log(`         Type: ${storageType}`);
        console.log(`         URL: ${file.path}`);
        console.log(`         Size: ${file.size} bytes`);
      });
    });

    console.log(`\n📊 Summary:`);
    console.log(`   Cloudinary files: ${cloudinaryCount}`);
    console.log(`   Local storage files: ${localCount}`);
    console.log(`   Other/Unknown files: ${otherCount}`);

    if (cloudinaryCount > 0) {
      console.log(`\n⚠️  Found ${cloudinaryCount} Cloudinary files that may cause issues`);
      console.log('   These are likely causing the Cloudinary errors you\'re seeing');
      console.log('   Run cleanup-old-attachments.js to remove them');
    }

    if (localCount > 0) {
      console.log(`\n✅ Found ${localCount} local storage files - these should work correctly`);
    }

  } catch (error) {
    console.error('❌ Error during inspection:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n📤 Disconnected from database');
  }
}

// Run the inspection
inspectAttachments();
