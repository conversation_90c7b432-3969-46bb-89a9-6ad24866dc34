// Simple test script to verify attachment upload functionality
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Local Attachment Storage Setup...\n');

// Test 1: Check if uploads directory exists
const uploadsDir = path.join(__dirname, 'uploads', 'attachments');
console.log('📁 Checking uploads directory...');
console.log('   Path:', uploadsDir);

if (fs.existsSync(uploadsDir)) {
  console.log('   ✅ Directory exists');
  
  // Check permissions
  try {
    fs.accessSync(uploadsDir, fs.constants.W_OK);
    console.log('   ✅ Directory is writable');
  } catch (error) {
    console.log('   ❌ Directory is not writable:', error.message);
  }
} else {
  console.log('   ❌ Directory does not exist');
  console.log('   🔧 Creating directory...');
  try {
    fs.mkdirSync(uploadsDir, { recursive: true });
    console.log('   ✅ Directory created successfully');
  } catch (error) {
    console.log('   ❌ Failed to create directory:', error.message);
  }
}

// Test 2: Check configuration files
console.log('\n⚙️  Checking configuration files...');

const configPath = path.join(__dirname, 'src', 'config', 'cloudinary.js');
if (fs.existsSync(configPath)) {
  console.log('   ✅ Cloudinary config exists');
  
  const configContent = fs.readFileSync(configPath, 'utf8');
  if (configContent.includes('multer.diskStorage')) {
    console.log('   ✅ Local storage configuration found');
  } else {
    console.log('   ❌ Local storage configuration not found');
  }
} else {
  console.log('   ❌ Cloudinary config not found');
}

// Test 3: Check server.js for static file serving
const serverPath = path.join(__dirname, 'server.js');
if (fs.existsSync(serverPath)) {
  console.log('   ✅ Server.js exists');
  
  const serverContent = fs.readFileSync(serverPath, 'utf8');
  if (serverContent.includes('/uploads')) {
    console.log('   ✅ Static file serving configured');
  } else {
    console.log('   ❌ Static file serving not configured');
  }
} else {
  console.log('   ❌ Server.js not found');
}

// Test 4: List existing files in uploads directory
console.log('\n📋 Existing files in uploads directory:');
try {
  const files = fs.readdirSync(uploadsDir);
  if (files.length === 0) {
    console.log('   📂 Directory is empty (this is normal for new setup)');
  } else {
    files.forEach(file => {
      const filePath = path.join(uploadsDir, file);
      const stats = fs.statSync(filePath);
      console.log(`   📄 ${file} (${stats.size} bytes, ${stats.mtime.toISOString()})`);
    });
  }
} catch (error) {
  console.log('   ❌ Error reading directory:', error.message);
}

console.log('\n🎯 Test Summary:');
console.log('   - If all checks pass, the local storage setup is ready');
console.log('   - Start the server with: npm run dev');
console.log('   - Test file upload through the appointment form');
console.log('   - Check console logs for upload confirmation');
console.log('   - Verify files appear in uploads/attachments directory');

console.log('\n📝 Next Steps:');
console.log('   1. Start backend server: cd appointment-system-backend && npm run dev');
console.log('   2. Start frontend: cd appointment-system && npm run dev');
console.log('   3. Test file upload through appointment form');
console.log('   4. Check admin panel for attachment display');
